#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 55 ayet chunk analizi

# En büyük sureler ve ayet sayıları
surahs = [
    ("Bakara", 286),
    ("<PERSON><PERSON><PERSON><PERSON>", 227), 
    ("<PERSON><PERSON><PERSON><PERSON><PERSON>", 206),
    ("<PERSON><PERSON><PERSON><PERSON> İmrân", 200),
    ("<PERSON><PERSON><PERSON><PERSON><PERSON>", 182),
    ("<PERSON><PERSON><PERSON>", 176),
    ("En'âm", 165),
    ("Tâ-Hâ", 135),
    ("Tevbe", 129),
    ("Nahl", 128),
    ("Hûd", 123),
    ("<PERSON><PERSON><PERSON>", 120),
    ("<PERSON><PERSON>'minûn", 118),
    ("En<PERSON>yâ", 112),
    ("<PERSON><PERSON>r<PERSON>", 111),
    ("<PERSON>", 111),
    ("Kehf", 110),
    ("<PERSON><PERSON>", 109)
]

print("=== 55 AYET CHUNK DETAYLI ANALİZİ ===")
print()
print("BÜYÜK SURELER İÇİN 55 AYET CHUNK HESAPLAMALARI:")
print("=" * 80)
print(f"{'Sure Adı':<15} {'Ayet':<5} {'Chunk':<7} {'Kalan':<7} {'Chunk Boyutları':<25} {'Toplam Chunk':<12}")
print("-" * 80)

total_chunks = 0
for name, verses in surahs:
    chunks = verses // 55
    remaining = verses % 55
    
    if chunks == 0:
        chunk_sizes = f"1x{verses}"
        total_chunk_count = 1
    elif remaining == 0:
        chunk_sizes = f"{chunks}x55"
        total_chunk_count = chunks
    else:
        chunk_sizes = f"{chunks}x55 + 1x{remaining}"
        total_chunk_count = chunks + 1
    
    total_chunks += total_chunk_count
    
    print(f"{name:<15} {verses:<5} {chunks:<7} {remaining:<7} {chunk_sizes:<25} {total_chunk_count:<12}")

print("-" * 80)
print(f"Toplam chunk sayısı (büyük sureler): {total_chunks}")
print()

# Dosya boyutu tahminleri
print("DOSYA BOYUTU TAHMİNLERİ (55 AYET CHUNK):")
print("=" * 60)

# Gerçek verilerden hesaplama
# Bakara: 286 ayet = 137KB (çeviri), 2.4MB (words), 437KB-7.4MB (tafsir)
# Ortalama ayet başına boyut:
translation_per_verse = 137 * 1024 / 286  # ~490 bytes per verse
words_per_verse = 2.4 * 1024 * 1024 / 286  # ~8.7KB per verse  
tafsir_per_verse_min = 437 * 1024 / 286  # ~1.5KB per verse (min)
tafsir_per_verse_max = 7.4 * 1024 * 1024 / 286  # ~26.8KB per verse (max)

print(f"Ayet başına ortalama boyutlar:")
print(f"- Çeviri: {translation_per_verse:.0f} bytes")
print(f"- Kelime analizi: {words_per_verse/1024:.1f} KB")
print(f"- Tefsir: {tafsir_per_verse_min/1024:.1f} KB - {tafsir_per_verse_max/1024:.1f} KB")
print()

print("55 AYET CHUNK BOYUTLARI:")
chunk_translation = 55 * translation_per_verse / 1024  # KB
chunk_words = 55 * words_per_verse / 1024 / 1024  # MB
chunk_tafsir_min = 55 * tafsir_per_verse_min / 1024  # KB
chunk_tafsir_max = 55 * tafsir_per_verse_max / 1024 / 1024  # MB

print(f"- Çeviri chunk: {chunk_translation:.0f} KB")
print(f"- Kelime analizi chunk: {chunk_words:.1f} MB")
print(f"- Tefsir chunk: {chunk_tafsir_min:.0f} KB - {chunk_tafsir_max:.1f} MB")
print()

print("PERFORMANS ETKİSİ:")
print("=" * 40)
print("Mevcut durum vs 55 ayet chunk:")
print(f"- Bakara çeviri: 137KB -> {chunk_translation:.0f}KB (chunk başına)")
print(f"- Bakara words: 2.4MB -> {chunk_words:.1f}MB (chunk başına)")
print(f"- Bakara tefsir: 437KB-7.4MB -> {chunk_tafsir_min:.0f}KB-{chunk_tafsir_max:.1f}MB (chunk başına)")
print()

# Bakara için chunk sayısı
bakara_chunks = 286 // 55 + (1 if 286 % 55 > 0 else 0)
print(f"Bakara suresi {bakara_chunks} chunk'a bölünecek:")
for i in range(bakara_chunks):
    start_verse = i * 55 + 1
    end_verse = min((i + 1) * 55, 286)
    verse_count = end_verse - start_verse + 1
    print(f"  Chunk {i+1}: Ayet {start_verse}-{end_verse} ({verse_count} ayet)")
