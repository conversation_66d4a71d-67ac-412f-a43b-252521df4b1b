#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# Kapsamlı chunk boyutu analizi

import os

# Gerçek dosya boyutlarını kontrol et
def get_file_size_mb(filepath):
    try:
        size_bytes = os.path.getsize(filepath)
        return size_bytes / (1024 * 1024)
    except:
        return 0

# Gerçek dosya boyutlarını ölç
bakara_translation = get_file_size_mb("data/quran/translations/diyanet_vakfi/2.json")
bakara_words = get_file_size_mb("data/quran/words/2.json")
bakara_tafsir = get_file_size_mb("data/tafsirs/30/2.json")

print("=== GERÇEK DOSYA BOYUTLARI (MB) ===")
print(f"Bakara Suresi (286 ayet):")
print(f"- Çeviri: {bakara_translation:.2f} MB")
print(f"- Kelime analizi: {bakara_words:.2f} MB") 
print(f"- Tefsir: {bakara_tafsir:.2f} MB")
print()

# Ayet başına ortalama boyut
translation_per_verse = bakara_translation / 286
words_per_verse = bakara_words / 286
tafsir_per_verse = bakara_tafsir / 286

print("AYET BAŞINA ORTALAMA BOYUT:")
print(f"- Çeviri: {translation_per_verse*1024:.1f} KB")
print(f"- Kelime analizi: {words_per_verse*1024:.1f} KB")
print(f"- Tefsir: {tafsir_per_verse*1024:.1f} KB")
print()

# Farklı chunk boyutları için analiz
chunk_sizes = [25, 30, 40, 50, 55, 60, 70]

print("=== CHUNK BOYUTU KARŞILAŞTIRMASI ===")
print(f"{'Chunk':<6} {'Çeviri':<8} {'Words':<8} {'Tefsir':<8} {'Bakara':<8} {'Toplam':<8}")
print(f"{'Boyutu':<6} {'(KB)':<8} {'(KB)':<8} {'(KB)':<8} {'Chunk':<8} {'Dosya':<8}")
print("-" * 55)

for chunk_size in chunk_sizes:
    # Chunk başına boyutlar
    trans_chunk = translation_per_verse * chunk_size * 1024  # KB
    words_chunk = words_per_verse * chunk_size * 1024  # KB  
    tafsir_chunk = tafsir_per_verse * chunk_size * 1024  # KB
    
    # Bakara için chunk sayısı
    bakara_chunks = 286 // chunk_size + (1 if 286 % chunk_size > 0 else 0)
    
    # Tüm sureler için tahmini toplam dosya sayısı (114 sure)
    # Ortalama ayet sayısı ~55, bu yüzden yaklaşık hesaplama
    avg_verses_per_surah = 55
    total_files = 0
    for verses in [286, 227, 206, 200, 182, 176, 165, 135, 129, 128]:  # En büyük 10 sure
        total_files += verses // chunk_size + (1 if verses % chunk_size > 0 else 0)
    
    # Kalan 104 sure için tahmini (ortalama 30 ayet)
    for i in range(104):
        avg_small = 30
        total_files += avg_small // chunk_size + (1 if avg_small % chunk_size > 0 else 0)
    
    print(f"{chunk_size:<6} {trans_chunk:<8.0f} {words_chunk:<8.0f} {tafsir_chunk:<8.0f} {bakara_chunks:<8} {total_files:<8}")

print()

# 55 ayet chunk için detaylı analiz
print("=== 55 AYET CHUNK DETAYLI ANALİZ ===")
print()

# En büyük sureler
big_surahs = [
    ("Bakara", 286),
    ("Şuarâ", 227), 
    ("A'râf", 206),
    ("Âl-i İmrân", 200),
    ("Sâffât", 182),
    ("Nisâ", 176),
    ("En'âm", 165),
    ("Tâ-Hâ", 135),
    ("Tevbe", 129),
    ("Nahl", 128)
]

print("BÜYÜK SURELER İÇİN 55 AYET CHUNK:")
print(f"{'Sure':<12} {'Ayet':<5} {'Chunk':<6} {'Kalan':<6} {'Chunk Detayı':<20}")
print("-" * 55)

total_chunks_55 = 0
for name, verses in big_surahs:
    chunks = verses // 55
    remaining = verses % 55
    total_chunk_count = chunks + (1 if remaining > 0 else 0)
    total_chunks_55 += total_chunk_count
    
    if remaining == 0:
        detail = f"{chunks} tam chunk"
    else:
        detail = f"{chunks}x55 + 1x{remaining}"
    
    print(f"{name:<12} {verses:<5} {total_chunk_count:<6} {remaining:<6} {detail:<20}")

print(f"\nToplam chunk (en büyük 10 sure): {total_chunks_55}")

# Dosya boyutu optimizasyonu
print("\n=== DOSYA BOYUTU OPTİMİZASYONU ===")
print("55 ayet chunk ile beklenen boyutlar:")

chunk_55_trans = translation_per_verse * 55 * 1024  # KB
chunk_55_words = words_per_verse * 55 * 1024  # KB
chunk_55_tafsir = tafsir_per_verse * 55 * 1024  # KB

print(f"- Çeviri chunk: {chunk_55_trans:.0f} KB")
print(f"- Kelime analizi chunk: {chunk_55_words:.0f} KB") 
print(f"- Tefsir chunk: {chunk_55_tafsir:.0f} KB")
print()

print("PERFORMANS İYİLEŞTİRMESİ:")
print(f"- Çeviri: {bakara_translation*1024:.0f}KB -> {chunk_55_trans:.0f}KB (%{(1-chunk_55_trans/(bakara_translation*1024))*100:.0f} azalma)")
print(f"- Words: {bakara_words*1024:.0f}KB -> {chunk_55_words:.0f}KB (%{(1-chunk_55_words/(bakara_words*1024))*100:.0f} azalma)")
print(f"- Tefsir: {bakara_tafsir*1024:.0f}KB -> {chunk_55_tafsir:.0f}KB (%{(1-chunk_55_tafsir/(bakara_tafsir*1024))*100:.0f} azalma)")
