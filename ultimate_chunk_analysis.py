#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

# Sure bilgilerini oku
with open('data/quran/surahs.json', 'r', encoding='utf-8') as f:
    surahs = json.load(f)

print("=== ULTIMATE CHUNK ANALİZİ ===")
print()

# Toplam ayet sayısını hesapla
total_verses = sum(s['verse_count'] for s in surahs)
print(f"Toplam ayet sayısı: {total_verses}")
print(f"Toplam sure sayısı: {len(surahs)}")
print()

# YAKLAŞIM 1: UNIFORM 55 AYET CHUNK
print("=== YAKLAŞIM 1: UNIFORM 55 AYET CHUNK ===")
uniform_chunks = total_verses // 55 + (1 if total_verses % 55 > 0 else 0)
print(f"Toplam chunk sayısı: {uniform_chunks}")
print(f"Son chunk'ta kalan ayet: {total_verses % 55}")
print()

# YAKLAŞIM 2: HİBRİT YAKLAŞIM (Küçük sureleri birleştir, büyükleri böl)
print("=== YAKLAŞIM 2: HİBRİT YAKLAŞIM ===")

# Sureleri boyutlarına göre kategorize et
small_surahs = []  # 30 ayetten az
medium_surahs = []  # 30-80 ayet arası
large_surahs = []  # 80+ ayet

for surah in surahs:
    if surah['verse_count'] <= 30:
        small_surahs.append(surah)
    elif surah['verse_count'] <= 80:
        medium_surahs.append(surah)
    else:
        large_surahs.append(surah)

print(f"Küçük sureler (≤30 ayet): {len(small_surahs)} sure")
print(f"Orta sureler (31-80 ayet): {len(medium_surahs)} sure") 
print(f"Büyük sureler (80+ ayet): {len(large_surahs)} sure")
print()

# Küçük sureleri birleştir
print("KÜÇÜK SURELER BİRLEŞTİRME:")
small_chunks = []
current_chunk = []
current_verses = 0

for surah in small_surahs:
    if current_verses + surah['verse_count'] <= 55:
        current_chunk.append(surah)
        current_verses += surah['verse_count']
    else:
        if current_chunk:
            small_chunks.append(current_chunk)
        current_chunk = [surah]
        current_verses = surah['verse_count']

if current_chunk:
    small_chunks.append(current_chunk)

print(f"Küçük sureler {len(small_chunks)} chunk'a birleştirildi:")
for i, chunk in enumerate(small_chunks):
    total_v = sum(s['verse_count'] for s in chunk)
    sure_names = [s['name'] for s in chunk]
    print(f"  Chunk {i+1}: {total_v} ayet - {', '.join(sure_names)}")

# Orta sureleri analiz et
print(f"\nORTA SURELER:")
medium_chunks = len(medium_surahs)  # Her biri tek chunk
print(f"Orta sureler {medium_chunks} chunk (her biri tek dosya)")

# Büyük sureleri böl
print(f"\nBÜYÜK SURELER BÖLME:")
large_chunks_total = 0
for surah in large_surahs:
    chunks = surah['verse_count'] // 55 + (1 if surah['verse_count'] % 55 > 0 else 0)
    large_chunks_total += chunks
    remaining = surah['verse_count'] % 55
    if remaining > 0:
        print(f"  {surah['name']} ({surah['verse_count']} ayet): {chunks} chunk (son chunk {remaining} ayet)")
    else:
        print(f"  {surah['name']} ({surah['verse_count']} ayet): {chunks} chunk (tam)")

# Hibrit toplam
hibrit_total = len(small_chunks) + medium_chunks + large_chunks_total
print(f"\nHİBRİT TOPLAM CHUNK: {hibrit_total}")

print("\n" + "="*60)
print("KARŞILAŞTIRMA:")
print(f"Uniform 55 ayet chunk: {uniform_chunks} dosya")
print(f"Hibrit yaklaşım: {hibrit_total} dosya")
print(f"Mevcut durum: {len(surahs)} dosya")
print()

# DOSYA İSİMLENDİRME ÖNERİLERİ
print("=== DOSYA İSİMLENDİRME ÖNERİLERİ ===")
print()

print("UNIFORM YAKLAŞIM İÇİN:")
print("Format: chunk_XXXX.json (4 haneli sıra numarası)")
print("Örnek: chunk_0001.json, chunk_0002.json, ...")
print("Metadata: chunk_index.json (hangi chunk'ta hangi ayetler)")
print()

print("HİBRİT YAKLAŞIM İÇİN:")
print("Küçük sureler: small_chunk_XX.json")
print("Orta sureler: surah_XXX.json (sure numarası)")
print("Büyük sureler: surah_XXX_part_X.json")
print()
print("Örnekler:")
print("- small_chunk_01.json (Fatiha + İhlas + Felak + Nas)")
print("- surah_036.json (Yasin suresi)")
print("- surah_002_part_1.json (Bakara 1-55)")
print("- surah_002_part_2.json (Bakara 56-110)")

# PERFORMANS ANALİZİ
print("\n=== PERFORMANS ANALİZİ ===")

# Gerçek dosya boyutlarından hesaplama
bakara_size_kb = 137  # KB
bakara_verses = 286
kb_per_verse = bakara_size_kb / bakara_verses

print(f"Ayet başına ortalama boyut: {kb_per_verse:.2f} KB")
print()

print("UNIFORM 55 AYET CHUNK:")
uniform_chunk_size = 55 * kb_per_verse
print(f"- Chunk boyutu: {uniform_chunk_size:.0f} KB")
print(f"- Yükleme süresi: ~{uniform_chunk_size/100:.1f} saniye (100KB/s)")
print()

print("HİBRİT YAKLAŞIM:")
# Küçük chunk'ların ortalama boyutu
avg_small_chunk_verses = sum(sum(s['verse_count'] for s in chunk) for chunk in small_chunks) / len(small_chunks)
small_chunk_size = avg_small_chunk_verses * kb_per_verse
print(f"- Küçük chunk ortalama: {small_chunk_size:.0f} KB")

# Orta chunk'ların ortalama boyutu  
avg_medium_verses = sum(s['verse_count'] for s in medium_surahs) / len(medium_surahs)
medium_chunk_size = avg_medium_verses * kb_per_verse
print(f"- Orta chunk ortalama: {medium_chunk_size:.0f} KB")

# Büyük chunk boyutu
large_chunk_size = 55 * kb_per_verse
print(f"- Büyük chunk boyutu: {large_chunk_size:.0f} KB")

print("\n=== SONUÇ VE ÖNERİ ===")
print()

if uniform_chunks < hibrit_total:
    print("🏆 UNIFORM 55 AYET CHUNK KAZANIYOR!")
    print(f"✅ Daha az dosya: {uniform_chunks} vs {hibrit_total}")
    print("✅ Daha basit yapı")
    print("✅ Tutarlı chunk boyutları")
    print("✅ Kolay metadata yönetimi")
else:
    print("🏆 HİBRİT YAKLAŞIM KAZANIYOR!")
    print(f"✅ Daha az dosya: {hibrit_total} vs {uniform_chunks}")
    print("✅ Sure bütünlüğü korunuyor")
    print("✅ Küçük sureler birlikte")
    print("✅ Daha mantıklı gruplama")
